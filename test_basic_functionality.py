"""
Basic functionality test for the Soil Rock Cone system.

This test verifies that the core components can be imported and
basic functionality works as expected.
"""

import sys
import os
import tempfile
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all core modules can be imported."""
    print("Testing imports...")
    
    try:
        from soil_rock_cone.data_models import PileData, SiteBoundary, ProcessingConfig
        from soil_rock_cone.core.data_processor import DataProcessor
        from soil_rock_cone.core.geometry_engine import GeometryEngine
        from soil_rock_cone.core.geometric_decomposer import GeometricDecomposer
        from soil_rock_cone.main import process_pile_volumes, stub_function_for_foundation_automation
        print("[PASS] All imports successful")
        return True
    except ImportError as e:
        print(f"[FAIL] Import failed: {e}")
        return False

def test_data_models():
    """Test data model validation."""
    print("Testing data models...")

    try:
        from soil_rock_cone.data_models import PileData, SiteBoundary, ProcessingConfig

        # Test PileData
        pile = PileData(
            pile_id="TEST001",
            center_x=10.0,
            center_y=10.0,
            pile_cap_bottom_level=100.0,
            target_level=95.0,
            founding_level=85.0,
            diameter=1.2,
            target_stratum="Dense Sand",
            socket_length=0.0
        )

        assert pile.material_type.value == "Soil"
        assert pile.frustum_angle_degrees == 15.0
        assert pile.part_1_height == 15.0

        # Test SiteBoundary
        boundary = SiteBoundary(coordinates=[(0, 0), (100, 0), (100, 100), (0, 100)])
        assert len(boundary.closed_coordinates) == 5  # Should be closed

        # Test ProcessingConfig
        config = ProcessingConfig()
        assert config.mesh_resolution == 0.1
        assert config.volume_tolerance == 0.001

        print("[PASS] Data models validation successful")
        return True
    except Exception as e:
        print(f"[FAIL] Data models test failed: {e}")
        return False

def test_stub_function():
    """Test the stub function for Foundation-Automation compatibility."""
    print("Testing stub function...")

    try:
        from soil_rock_cone.main import stub_function_for_foundation_automation

        result = stub_function_for_foundation_automation()
        assert isinstance(result, str)
        assert "ready for integration" in result

        print("[PASS] Stub function test successful")
        return True
    except Exception as e:
        print(f"[FAIL] Stub function test failed: {e}")
        return False

def test_basic_processing():
    """Test basic processing pipeline with mock data."""
    print("Testing basic processing...")

    try:
        from soil_rock_cone.main import process_pile_volumes

        # Create a temporary Excel file (mock)
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            tmp_excel_path = tmp_file.name

        # Define test site boundary
        site_boundary = [(0, 0), (50, 0), (50, 50), (0, 50)]

        # Test configuration
        config = {
            "mesh_resolution": 0.5,  # Coarser for faster testing
            "volume_tolerance": 0.01,
            "output_step_files": False,
            "output_visualizations": False
        }

        # Run processing (should handle missing Excel file gracefully)
        result = process_pile_volumes(
            excel_file_path=tmp_excel_path,
            site_boundary_coords=site_boundary,
            output_directory="test_output",
            config=config
        )

        # Clean up
        os.unlink(tmp_excel_path)

        # Check result structure
        assert isinstance(result, dict)
        assert 'success' in result
        assert 'processing_time' in result

        print("[PASS] Basic processing test completed")
        return True
    except Exception as e:
        print(f"[FAIL] Basic processing test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== Soil Rock Cone System - Basic Functionality Test ===\n")
    
    tests = [
        test_imports,
        test_data_models,
        test_stub_function,
        test_basic_processing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"=== Test Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        print("SUCCESS: All tests passed! The basic system is working correctly.")
        return True
    else:
        print("FAILURE: Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)