"""
Data models for the Soil Rock Cone system using Pydantic for validation.

This module defines the core data structures used throughout the system:
- PileData: Individual pile parameters
- SiteBoundary: Site boundary polygon
- GeometricObject: Decomposed 3D objects
- VolumeReport: Volume calculation results
"""

from typing import List, Tuple, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
import numpy as np
from enum import Enum


class MaterialType(str, Enum):
    """Material classification for pile parts."""
    SOIL = "Soil"
    ROCK = "Rock"


class PartType(str, Enum):
    """Pile part classification."""
    PART_1 = "Part 1"  # Pile cylinder (voiding agent)
    PART_2 = "Part 2"  # Soil/Rock frustum with cylindrical void
    PART_3 = "Part 3"  # Soil cylinder with cylindrical void


class PileData(BaseModel):
    """
    Data model for individual pile parameters.
    
    Represents all geometric and material properties needed
    to create 3D pile geometries and perform volume calculations.
    """
    pile_id: str = Field(..., description="Unique pile identifier")
    center_x: float = Field(..., description="Pile center X coordinate")
    center_y: float = Field(..., description="Pile center Y coordinate")
    pile_cap_bottom_level: float = Field(..., description="Pile cap bottom level (top of geometry)")
    target_level: float = Field(..., description="Target level (middle of geometry)")
    founding_level: float = Field(..., description="Founding level (bottom of geometry)")
    diameter: float = Field(..., gt=0, description="Pile diameter in meters")
    target_stratum: str = Field(..., description="Target stratum description")
    socket_length: float = Field(..., ge=0, description="Socket length in meters")
    
    @validator('target_level')
    def target_level_validation(cls, v, values):
        """Validate that target level is between pile cap bottom and founding level."""
        if 'pile_cap_bottom_level' in values and 'founding_level' in values:
            pile_cap = values['pile_cap_bottom_level']
            founding = values['founding_level']
            if not (founding <= v <= pile_cap):
                raise ValueError(f"Target level {v} must be between founding level {founding} and pile cap bottom {pile_cap}")
        return v
    
    @validator('founding_level')
    def founding_level_validation(cls, v, values):
        """Validate that founding level is below pile cap bottom level."""
        if 'pile_cap_bottom_level' in values:
            pile_cap = values['pile_cap_bottom_level']
            if v >= pile_cap:
                raise ValueError(f"Founding level {v} must be below pile cap bottom level {pile_cap}")
        return v
    
    @property
    def material_type(self) -> MaterialType:
        """
        Determine material type based on target stratum and socket length.
        
        Returns:
            MaterialType.ROCK if socket_length > 0, otherwise MaterialType.SOIL
        """
        return MaterialType.ROCK if self.socket_length > 0 else MaterialType.SOIL
    
    @property
    def frustum_angle_degrees(self) -> float:
        """
        Get frustum projection angle based on material type.
        
        Returns:
            30° for Rock, 15° for Soil
        """
        return 30.0 if self.material_type == MaterialType.ROCK else 15.0
    
    @property
    def part_1_height(self) -> float:
        """Height of Part 1 (pile cylinder)."""
        return self.pile_cap_bottom_level - self.founding_level
    
    @property
    def part_2_height(self) -> float:
        """Height of Part 2 (frustum)."""
        return self.pile_cap_bottom_level - self.target_level
    
    @property
    def part_3_height(self) -> float:
        """Height of Part 3 (soil cylinder)."""
        return self.target_level - self.founding_level

    def determine_material_type(self) -> MaterialType:
        """
        Determine material type based on target stratum and socket length.

        Returns:
            MaterialType.ROCK if target stratum contains "Rock" and socket length > 0,
            MaterialType.SOIL otherwise
        """
        return self.material_type


class SiteBoundary(BaseModel):
    """
    Data model for site boundary polygon.
    
    Represents the 2D polygon that defines the site limits
    for volume clipping operations.
    """
    coordinates: List[Tuple[float, float]] = Field(..., min_items=3, description="Boundary coordinates as (x, y) tuples")
    
    @validator('coordinates')
    def validate_coordinates(cls, v):
        """Validate that coordinates form a valid polygon."""
        if len(v) < 3:
            raise ValueError("Site boundary must have at least 3 coordinates")
        
        # Check for duplicate consecutive points
        for i in range(len(v)):
            current = v[i]
            next_point = v[(i + 1) % len(v)]
            if abs(current[0] - next_point[0]) < 1e-6 and abs(current[1] - next_point[1]) < 1e-6:
                raise ValueError(f"Duplicate consecutive coordinates at index {i}: {current}")
        
        return v
    
    @property
    def is_closed(self) -> bool:
        """Check if the polygon is closed (first and last points are the same)."""
        if len(self.coordinates) < 2:
            return False
        first = self.coordinates[0]
        last = self.coordinates[-1]
        return abs(first[0] - last[0]) < 1e-6 and abs(first[1] - last[1]) < 1e-6
    
    @property
    def closed_coordinates(self) -> List[Tuple[float, float]]:
        """Get coordinates ensuring the polygon is closed."""
        if self.is_closed:
            return self.coordinates
        return self.coordinates + [self.coordinates[0]]


class GeometricObject(BaseModel):
    """
    Data model for decomposed 3D geometric objects.
    
    Represents individual geometric objects created by the
    decomposition algorithm, with volume and allocation information.
    """
    object_id: str = Field(..., description="Unique object identifier")
    contributing_piles: List[str] = Field(..., min_items=1, description="List of pile IDs that contribute to this object")
    part_type: PartType = Field(..., description="Type of pile part")
    material_type: MaterialType = Field(..., description="Material classification")
    volume: float = Field(..., ge=0, description="Object volume in cubic meters")
    allocation_factor: float = Field(..., gt=0, le=1, description="Allocation factor for volume distribution")
    centroid: Optional[Tuple[float, float, float]] = Field(None, description="Centroid coordinates (x, y, z)")
    geometry: Optional[Any] = Field(None, description="Trimesh geometry object")
    mesh_data: Optional[Dict[str, Any]] = Field(None, description="Serialized mesh data for reconstruction")
    
    @validator('allocation_factor')
    def validate_allocation_factor(cls, v, values):
        """Validate allocation factor based on contributing piles."""
        if 'contributing_piles' in values:
            expected_factor = 1.0 / len(values['contributing_piles'])
            if abs(v - expected_factor) > 1e-6:
                raise ValueError(f"Allocation factor {v} should be {expected_factor} for {len(values['contributing_piles'])} contributing piles")
        return v
    
    @property
    def allocated_volume(self) -> float:
        """Get volume allocated to each contributing pile."""
        return self.volume * self.allocation_factor


class VolumeReport(BaseModel):
    """
    Data model for volume calculation results per pile.
    
    Aggregates volume contributions from all geometric objects
    associated with a specific pile.
    """
    pile_id: str = Field(..., description="Pile identifier")
    material_type: MaterialType = Field(..., description="Material classification")

    # Original part volumes
    part1_volume: float = Field(..., ge=0, description="Part 1 original volume in cubic meters")
    part2_volume: float = Field(..., ge=0, description="Part 2 original volume in cubic meters")
    part3_volume: float = Field(..., ge=0, description="Part 3 original volume in cubic meters")

    # Overlap volumes allocated from other piles
    part1_overlap_volume: float = Field(0.0, ge=0, description="Part 1 overlap volume allocated from other piles")
    part2_overlap_volume: float = Field(0.0, ge=0, description="Part 2 overlap volume allocated from other piles")
    part3_overlap_volume: float = Field(0.0, ge=0, description="Part 3 overlap volume allocated from other piles")

    # Residual volumes after overlap subtraction
    part1_residual_volume: float = Field(..., ge=0, description="Part 1 residual volume after overlap subtraction")
    part2_residual_volume: float = Field(..., ge=0, description="Part 2 residual volume after overlap subtraction")
    part3_residual_volume: float = Field(..., ge=0, description="Part 3 residual volume after overlap subtraction")

    # Total volumes
    total_overlap_volume: float = Field(0.0, ge=0, description="Total overlap volume allocated to this pile")
    total_residual_volume: float = Field(..., ge=0, description="Total residual volume for this pile")
    total_volume: float = Field(..., ge=0, description="Grand total volume allocated to this pile")


class ProcessingConfig(BaseModel):
    """
    Configuration parameters for the processing pipeline.
    """
    mesh_resolution: float = Field(0.1, gt=0, description="Mesh resolution for 3D operations")
    volume_tolerance: float = Field(0.001, gt=0, le=0.01, description="Volume conservation tolerance (0.1%)")
    spatial_tolerance: float = Field(1e-6, gt=0, description="Spatial tolerance for geometric operations")
    max_clique_size: int = Field(20, gt=0, description="Maximum clique size for overlap processing")
    enable_parallel_processing: bool = Field(True, description="Enable parallel processing for performance")
    output_step_files: bool = Field(True, description="Generate STEP file outputs")
    output_visualizations: bool = Field(False, description="Generate visualization outputs")
    enable_mesh_repair: bool = Field(True, description="Enable automatic mesh repair for invalid geometries")
    overlap_detection_tolerance: float = Field(0.01, gt=0, description="Tolerance for overlap detection (m)")
    
    class Config:
        """Pydantic configuration."""
        extra = "forbid"  # Prevent additional fields