"""
Excel Reader Module for Soil Rock Cone Analysis

This module provides functionality to read pile data from Excel files
according to the specification format. It handles the concatenation of
BP, SHP, DHP, and MP sheets into a unified pile dataset.
"""

import pandas as pd
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from ..utils.logging_config import get_logger

logger = get_logger(__name__)


class ExcelInput:
    """Container class for Excel input data matching Foundation-Automation format."""
    
    def __init__(self, pile_data: pd.DataFrame, site_boundary_data: pd.DataFrame):
        self.Pile = pile_data
        self.SiteBoundary = site_boundary_data


class ExcelReader:
    """
    Excel file reader for pile volume analysis data.
    
    Reads Excel files containing pile data across multiple sheets (BP, SHP, DHP, MP)
    and site boundary information according to the specification format.
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def read_excel_file(self, file_path: str) -> ExcelInput:
        """
        Read Excel file and return structured data.
        
        Args:
            file_path: Path to the Excel file
            
        Returns:
            ExcelInput object containing pile and site boundary data
            
        Raises:
            FileNotFoundError: If the Excel file doesn't exist
            ValueError: If required sheets are missing or data is invalid
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Excel file not found: {file_path}")
        
        self.logger.info(f"Reading Excel file: {file_path}")
        
        try:
            # Load Excel file
            excel_data = pd.ExcelFile(file_path)
            available_sheets = excel_data.sheet_names
            self.logger.info(f"Available sheets: {available_sheets}")
            
            # Read pile data sheets
            pile_data = self._read_pile_data(excel_data, available_sheets)
            
            # Read site boundary data
            site_boundary_data = self._read_site_boundary_data(excel_data, available_sheets)
            
            # Validate data
            self._validate_data(pile_data, site_boundary_data)
            
            self.logger.info(f"Successfully loaded {len(pile_data)} piles and site boundary with {len(site_boundary_data)} points")
            
            return ExcelInput(pile_data, site_boundary_data)
            
        except Exception as e:
            self.logger.error(f"Error reading Excel file: {e}")
            raise
    
    def _read_pile_data(self, excel_data: pd.ExcelFile, available_sheets: list) -> pd.DataFrame:
        """Read and concatenate pile data from multiple sheets."""
        
        # Define expected pile data sheets
        pile_sheets = ['BP', 'SHP', 'DHP', 'MP']
        pile_dataframes = []
        
        for sheet_name in pile_sheets:
            if sheet_name in available_sheets:
                try:
                    df = excel_data.parse(sheet_name)
                    if not df.empty:
                        df['source_sheet'] = sheet_name  # Track source for debugging
                        pile_dataframes.append(df)
                        self.logger.info(f"Loaded {len(df)} rows from sheet '{sheet_name}'")
                    else:
                        self.logger.warning(f"Sheet '{sheet_name}' is empty")
                except Exception as e:
                    self.logger.warning(f"Could not read sheet '{sheet_name}': {e}")
            else:
                self.logger.warning(f"Expected sheet '{sheet_name}' not found")
        
        if not pile_dataframes:
            raise ValueError("No valid pile data sheets found. Expected at least one of: BP, SHP, DHP, MP")
        
        # Concatenate all pile data
        pile_data = pd.concat(pile_dataframes, ignore_index=True)
        
        # Standardize column names
        pile_data = self._standardize_pile_columns(pile_data)
        
        return pile_data
    
    def _read_site_boundary_data(self, excel_data: pd.ExcelFile, available_sheets: list) -> pd.DataFrame:
        """Read site boundary data."""
        
        if 'SiteBoundary' not in available_sheets:
            raise ValueError("Required sheet 'SiteBoundary' not found")
        
        try:
            site_boundary_data = excel_data.parse('SiteBoundary')
            self.logger.info(f"Loaded site boundary with {len(site_boundary_data)} points")
            return site_boundary_data
        except Exception as e:
            raise ValueError(f"Error reading SiteBoundary sheet: {e}")
    
    def _standardize_pile_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardize column names to match specification requirements.
        
        Maps various possible column names to the standard format.
        """
        
        # Define column mapping (flexible to handle variations)
        column_mapping = {
            # Pile identification
            'pile_mark': 'Pile Mark',
            'pile mark': 'Pile Mark',
            'mark': 'Pile Mark',
            'id': 'Pile Mark',
            
            # Coordinates
            'x': 'X (m)',
            'x (m)': 'X (m)',
            'x_coord': 'X (m)',
            'x_coordinate': 'X (m)',
            'easting': 'X (m)',
            
            'y': 'Y (m)',
            'y (m)': 'Y (m)',
            'y_coord': 'Y (m)',
            'y_coordinate': 'Y (m)',
            'northing': 'Y (m)',
            
            # Levels
            'pile_cap_bottom_level': 'Pile Cap Bottom Level (mPD)',
            'pile cap bottom level': 'Pile Cap Bottom Level (mPD)',
            'pile_cap_bottom_level_(mpd)': 'Pile Cap Bottom Level (mPD)',
            'cap_level': 'Pile Cap Bottom Level (mPD)',
            
            'founding_level': 'Founding Level (mPD)',
            'founding level': 'Founding Level (mPD)',
            'founding_level_(mpd)': 'Founding Level (mPD)',
            'final_level': 'Founding Level (mPD)',
            
            # Diameter
            'diameter': 'Pile Shaft Diameter (m)',
            'pile_shaft_diameter': 'Pile Shaft Diameter (m)',
            'pile shaft diameter': 'Pile Shaft Diameter (m)',
            'pile_shaft_diameter_(m)': 'Pile Shaft Diameter (m)',
            'shaft_diameter': 'Pile Shaft Diameter (m)',
            
            # Target stratum and levels
            'target_stratum': 'Target Stratum',
            'target stratum': 'Target Stratum',
            'stratum': 'Target Stratum',
            
            'socket_length': 'Socket Length (m)',
            'socket length': 'Socket Length (m)',
            'socket_length_(m)': 'Socket Length (m)',
            
            # Stratum levels
            'sptn30_level': 'SPTN30 Level (mPD)',
            'sptn30 level': 'SPTN30 Level (mPD)',
            'sptn30_level_(mpd)': 'SPTN30 Level (mPD)',
            
            'rock1a_level': 'Rock1a Level (mPD)',
            'rock1a level': 'Rock1a Level (mPD)',
            'rock1a_level_(mpd)': 'Rock1a Level (mPD)',
            
            'rock1b_level': 'Rock1b Level (mPD)',
            'rock1b level': 'Rock1b Level (mPD)',
            'rock1b_level_(mpd)': 'Rock1b Level (mPD)',
            
            'rock1c_level': 'Rock1c Level (mPD)',
            'rock1c level': 'Rock1c Level (mPD)',
            'rock1c_level_(mpd)': 'Rock1c Level (mPD)',
            
            'rock1d_level': 'Rock1d Level (mPD)',
            'rock1d level': 'Rock1d Level (mPD)',
            'rock1d_level_(mpd)': 'Rock1d Level (mPD)',
            
            'rock2_level': 'Rock2 Level (mPD)',
            'rock2 level': 'Rock2 Level (mPD)',
            'rock2_level_(mpd)': 'Rock2 Level (mPD)',
        }
        
        # Apply case-insensitive column mapping
        df_columns_lower = {col.lower(): col for col in df.columns}
        
        for old_name, new_name in column_mapping.items():
            if old_name.lower() in df_columns_lower:
                original_col = df_columns_lower[old_name.lower()]
                df = df.rename(columns={original_col: new_name})
                self.logger.debug(f"Mapped column '{original_col}' to '{new_name}'")
        
        return df
    
    def _validate_data(self, pile_data: pd.DataFrame, site_boundary_data: pd.DataFrame):
        """Validate the loaded data for completeness and consistency."""
        
        # Required pile columns
        required_pile_columns = [
            'Pile Mark',
            'X (m)',
            'Y (m)',
            'Pile Cap Bottom Level (mPD)',
            'Founding Level (mPD)',
            'Pile Shaft Diameter (m)',
            'Target Stratum',
            'Socket Length (m)'
        ]
        
        # Check for required columns
        missing_columns = []
        for col in required_pile_columns:
            if col not in pile_data.columns:
                missing_columns.append(col)
        
        if missing_columns:
            raise ValueError(f"Missing required pile data columns: {missing_columns}")
        
        # Check for empty data
        if pile_data.empty:
            raise ValueError("Pile data is empty")
        
        if site_boundary_data.empty:
            raise ValueError("Site boundary data is empty")
        
        # Check for duplicate pile marks
        duplicate_marks = pile_data['Pile Mark'].duplicated()
        if duplicate_marks.any():
            duplicates = pile_data.loc[duplicate_marks, 'Pile Mark'].tolist()
            raise ValueError(f"Duplicate pile marks found: {duplicates}")
        
        # Validate numeric columns
        numeric_columns = [
            'X (m)', 'Y (m)', 'Pile Cap Bottom Level (mPD)', 
            'Founding Level (mPD)', 'Pile Shaft Diameter (m)', 'Socket Length (m)'
        ]
        
        for col in numeric_columns:
            if col in pile_data.columns:
                if not pd.api.types.is_numeric_dtype(pile_data[col]):
                    try:
                        pile_data[col] = pd.to_numeric(pile_data[col], errors='coerce')
                    except:
                        raise ValueError(f"Column '{col}' contains non-numeric values")
                
                # Check for NaN values
                if pile_data[col].isna().any():
                    nan_count = pile_data[col].isna().sum()
                    raise ValueError(f"Column '{col}' contains {nan_count} missing values")
        
        # Validate positive diameter
        if (pile_data['Pile Shaft Diameter (m)'] <= 0).any():
            raise ValueError("Pile shaft diameter must be positive")
        
        # Validate socket length (non-negative)
        if (pile_data['Socket Length (m)'] < 0).any():
            raise ValueError("Socket length must be non-negative")
        
        # Validate site boundary has at least 3 points
        if len(site_boundary_data) < 3:
            raise ValueError("Site boundary must have at least 3 points")
        
        self.logger.info("Data validation completed successfully")


def stub_read_excel_to_excel_input(file_path: str) -> ExcelInput:
    """
    Stub implementation for Excel reading when Foundation-Automation is unavailable.
    
    This function provides the interface specified in the requirements document.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        ExcelInput object containing pile and site boundary data
    """
    reader = ExcelReader()
    return reader.read_excel_file(file_path)


# Convenience function for direct usage
def read_excel_file(file_path: str) -> ExcelInput:
    """
    Read Excel file and return structured data.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        ExcelInput object containing pile and site boundary data
    """
    return stub_read_excel_to_excel_input(file_path)