"""
Core processing modules for the Soil Rock Cone system.

This package contains the main processing logic:
- DataProcessor: Main orchestrator for the analysis pipeline
- GeometryEngine: 3D geometry creation and manipulation
- GeometricDecomposer: Overlap detection and decomposition algorithms
"""

from .data_processor import DataProcessor
from .geometry_engine import Geometry<PERSON>ngine
from .geometric_decomposer import GeometricDecomposer
from .excel_reader import ExcelReader, ExcelInput, read_excel_file, stub_read_excel_to_excel_input
from .cad_exporter import CADExporter
from .csv_exporter import CSVExporter

__all__ = [
    "DataProcessor",
    "GeometryEngine",
    "GeometricDecomposer",
    "ExcelReader",
    "ExcelInput",
    "read_excel_file",
    "stub_read_excel_to_excel_input",
    "CADExporter",
    "CSVExporter"
]