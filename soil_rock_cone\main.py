"""
Main entry point for the Soil Rock Cone pile volume analysis system.

This module provides the primary interface for processing pile volumes,
including stub function compatibility with Foundation-Automation.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional
import pandas as pd

from .core.data_processor import DataProcessor
from .utils.logging_config import setup_logging


def process_pile_volumes(
    excel_file_path: str,
    site_boundary_coords: list,
    output_directory: str = "output",
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Process pile volumes with 3D geometric analysis and decomposition.
    
    This is the main entry point for the pile volume analysis system.
    It processes Excel data, creates 3D geometries, handles overlaps,
    and generates comprehensive reports.
    
    Args:
        excel_file_path: Path to Excel file with pile data
        site_boundary_coords: List of (x, y) coordinates defining site boundary
        output_directory: Directory for output files (default: "output")
        config: Optional configuration dictionary
        
    Returns:
        Dictionary containing:
        - 'success': Boolean indicating success/failure
        - 'pile_reports': DataFrame with individual pile volumes
        - 'overlap_reports': DataFrame with overlap analysis
        - 'summary': Dictionary with project totals
        - 'output_files': List of generated file paths
        - 'processing_time': Time taken in seconds
        
    Raises:
        FileNotFoundError: If Excel file doesn't exist
        ValueError: If site boundary is invalid
        RuntimeError: If processing fails
    """
    # Set up logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"Starting pile volume analysis for {excel_file_path}")
        
        # Initialize data processor
        processor = DataProcessor(config or {})
        
        # Process the data
        result = processor.process(
            excel_file_path=excel_file_path,
            site_boundary_coords=site_boundary_coords,
            output_directory=output_directory
        )
        
        logger.info("Pile volume analysis completed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Pile volume analysis failed: {str(e)}")
        raise


# Stub function for Foundation-Automation compatibility
def stub_function_for_foundation_automation():
    """
    Stub function to maintain compatibility with Foundation-Automation system.
    
    This function serves as a placeholder for integration with the broader
    Foundation-Automation framework. It can be extended as needed for
    specific integration requirements.
    
    Returns:
        str: Status message indicating stub function execution
    """
    return "Soil Rock Cone pile volume analysis system is ready for integration"


if __name__ == "__main__":
    # Example usage for testing
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python -m soil_rock_cone.main <excel_file> <boundary_coords>")
        print("Example boundary_coords: '[(0,0),(100,0),(100,100),(0,100)]'")
        sys.exit(1)
    
    excel_file = sys.argv[1]
    # Simple parsing of boundary coordinates from command line
    # In production, this would be more robust
    boundary_str = sys.argv[2]
    try:
        boundary_coords = eval(boundary_str)  # Note: eval is unsafe, use proper parsing in production
    except:
        print("Invalid boundary coordinates format")
        sys.exit(1)
    
    result = process_pile_volumes(excel_file, boundary_coords)
    print(f"Processing completed: {result['success']}")
    if result['success']:
        print(f"Generated {len(result['output_files'])} output files")
        print(f"Processing time: {result['processing_time']:.2f} seconds")