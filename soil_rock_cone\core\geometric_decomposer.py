"""
Geometric Decomposer for the Soil Rock Cone system.

This module handles the decomposition of overlapping pile geometries
into mutually exclusive geometric objects using:
- Spatial indexing with KDTree for efficient overlap detection
- NetworkX graphs for pile connectivity analysis
- Multi-level exclusion algorithm for volume decomposition
- Volume conservation validation
"""

import logging
import numpy as np
import networkx as nx
from typing import Dict, List, Any, Set, Tuple
from scipy.spatial import KDTree
import trimesh
import uuid

from ..data_models import (
    ProcessingConfig, GeometricObject, PartType, MaterialType
)
from ..utils.logging_config import get_logger


class GeometricDecomposer:
    """
    Handles decomposition of overlapping pile geometries.
    
    Uses spatial indexing and graph algorithms to efficiently detect
    overlaps and create mutually exclusive geometric objects.
    """
    
    def __init__(self, config: ProcessingConfig):
        """
        Initialize the geometric decomposer.
        
        Args:
            config: Processing configuration parameters
        """
        self.logger = get_logger(__name__)
        self.config = config
        
    def decompose(self, pile_geometries: Dict[str, Dict[str, trimesh.Trimesh]]) -> List[GeometricObject]:
        """
        Decompose overlapping pile geometries into mutually exclusive objects.
        
        Args:
            pile_geometries: Dictionary mapping pile IDs to their geometries
            
        Returns:
            List of decomposed geometric objects
        """
        self.logger.info("Starting geometric decomposition")
        
        # Step 1: Build spatial index for efficient overlap detection
        spatial_index = self._build_spatial_index(pile_geometries)
        
        # Step 2: Detect overlaps using spatial queries
        overlaps = self._detect_overlaps(pile_geometries, spatial_index)
        
        # Step 3: Build connectivity graph
        connectivity_graph = self._build_connectivity_graph(overlaps)
        
        # Step 4: Find connected components (cliques)
        cliques = self._find_cliques(connectivity_graph)
        
        # Step 5: Decompose each clique
        geometric_objects = []
        for clique in cliques:
            clique_objects = self._decompose_clique(clique, pile_geometries)
            geometric_objects.extend(clique_objects)
        
        # Step 6: Validate volume conservation
        self._validate_volume_conservation(pile_geometries, geometric_objects)
        
        self.logger.info(f"Decomposition complete: {len(geometric_objects)} objects created")
        return geometric_objects
    
    def _build_spatial_index(
        self, 
        pile_geometries: Dict[str, Dict[str, trimesh.Trimesh]]
    ) -> Dict[str, KDTree]:
        """
        Build spatial index for efficient overlap detection.
        
        Args:
            pile_geometries: Pile geometries dictionary
            
        Returns:
            Dictionary mapping part types to KDTree indices
        """
        self.logger.debug("Building spatial index")
        
        spatial_index = {}
        
        for part_type in ['part_1', 'part_2', 'part_3']:
            centers = []
            pile_ids = []
            
            for pile_id, geometries in pile_geometries.items():
                if part_type in geometries and geometries[part_type] is not None:
                    # Get geometry center
                    center = geometries[part_type].center_mass
                    centers.append(center)
                    pile_ids.append(pile_id)
            
            if centers:
                kdtree = KDTree(np.array(centers))
                spatial_index[part_type] = {
                    'kdtree': kdtree,
                    'pile_ids': pile_ids
                }
        
        return spatial_index
    
    def _detect_overlaps(
        self,
        pile_geometries: Dict[str, Dict[str, trimesh.Trimesh]],
        spatial_index: Dict[str, Any]
    ) -> List[Tuple[str, str, str]]:
        """
        Detect overlapping geometries using spatial queries.
        
        Args:
            pile_geometries: Pile geometries dictionary
            spatial_index: Spatial index for efficient queries
            
        Returns:
            List of overlap tuples (pile_id_1, pile_id_2, part_type)
        """
        self.logger.debug("Detecting overlaps")
        
        overlaps = []
        
        for part_type in ['part_1', 'part_2', 'part_3']:
            if part_type not in spatial_index:
                continue
                
            kdtree = spatial_index[part_type]['kdtree']
            pile_ids = spatial_index[part_type]['pile_ids']
            
            # Query for potential overlaps within reasonable distance
            for i, pile_id_1 in enumerate(pile_ids):
                geometry_1 = pile_geometries[pile_id_1][part_type]
                if geometry_1 is None:
                    continue
                
                # Get bounding box diagonal as search radius
                bounds_1 = geometry_1.bounds
                search_radius = np.linalg.norm(bounds_1[1] - bounds_1[0])
                
                # Query spatial index
                center_1 = geometry_1.center_mass
                neighbor_indices = kdtree.query_ball_point(center_1, search_radius)
                
                for j in neighbor_indices:
                    if i >= j:  # Avoid duplicate pairs and self-comparison
                        continue
                        
                    pile_id_2 = pile_ids[j]
                    geometry_2 = pile_geometries[pile_id_2][part_type]
                    
                    if geometry_2 is None:
                        continue
                    
                    # Check for actual geometric overlap
                    if self._geometries_overlap(geometry_1, geometry_2):
                        overlaps.append((pile_id_1, pile_id_2, part_type))
        
        self.logger.debug(f"Found {len(overlaps)} overlaps")
        return overlaps
    
    def _geometries_overlap(
        self, 
        geometry_1: trimesh.Trimesh, 
        geometry_2: trimesh.Trimesh
    ) -> bool:
        """
        Check if two geometries actually overlap.
        
        Args:
            geometry_1: First geometry
            geometry_2: Second geometry
            
        Returns:
            True if geometries overlap
        """
        try:
            # Quick bounding box check first
            bounds_1 = geometry_1.bounds
            bounds_2 = geometry_2.bounds
            
            # Check if bounding boxes overlap
            if not self._bounding_boxes_overlap(bounds_1, bounds_2):
                return False
            
            # Perform boolean intersection to check for actual overlap
            intersection = geometry_1.intersection(geometry_2)
            
            # Check if intersection has significant volume
            return intersection.volume > self.config.spatial_tolerance
            
        except Exception as e:
            self.logger.warning(f"Failed to check geometry overlap: {str(e)}")
            return False
    
    def _bounding_boxes_overlap(
        self, 
        bounds_1: np.ndarray, 
        bounds_2: np.ndarray
    ) -> bool:
        """
        Check if two bounding boxes overlap.
        
        Args:
            bounds_1: First bounding box [min, max]
            bounds_2: Second bounding box [min, max]
            
        Returns:
            True if bounding boxes overlap
        """
        # Check overlap in each dimension
        for i in range(3):  # x, y, z
            if bounds_1[1][i] < bounds_2[0][i] or bounds_2[1][i] < bounds_1[0][i]:
                return False
        return True
    
    def _build_connectivity_graph(
        self, 
        overlaps: List[Tuple[str, str, str]]
    ) -> nx.Graph:
        """
        Build connectivity graph from overlap relationships.
        
        Args:
            overlaps: List of overlap tuples
            
        Returns:
            NetworkX graph representing pile connectivity
        """
        self.logger.debug("Building connectivity graph")
        
        graph = nx.Graph()
        
        for pile_id_1, pile_id_2, part_type in overlaps:
            # Add edge with part type as attribute
            if graph.has_edge(pile_id_1, pile_id_2):
                # Add part type to existing edge
                graph[pile_id_1][pile_id_2]['part_types'].add(part_type)
            else:
                # Create new edge
                graph.add_edge(pile_id_1, pile_id_2, part_types={part_type})
        
        self.logger.debug(f"Graph has {graph.number_of_nodes()} nodes and {graph.number_of_edges()} edges")
        return graph
    
    def _find_cliques(self, graph: nx.Graph) -> List[Set[str]]:
        """
        Find connected components (cliques) in the connectivity graph.
        
        Args:
            graph: Connectivity graph
            
        Returns:
            List of cliques (sets of connected pile IDs)
        """
        self.logger.debug("Finding cliques")
        
        # Find connected components
        connected_components = list(nx.connected_components(graph))
        
        # Filter large cliques if they exceed maximum size
        cliques = []
        for component in connected_components:
            if len(component) <= self.config.max_clique_size:
                cliques.append(component)
            else:
                self.logger.warning(f"Large clique with {len(component)} piles exceeds maximum size {self.config.max_clique_size}")
                # For large cliques, break them down using more sophisticated algorithms
                sub_cliques = self._decompose_large_clique(component, graph)
                cliques.extend(sub_cliques)
        
        self.logger.debug(f"Found {len(cliques)} cliques")
        return cliques
    
    def _decompose_large_clique(
        self, 
        large_clique: Set[str], 
        graph: nx.Graph
    ) -> List[Set[str]]:
        """
        Decompose large clique into smaller manageable cliques.
        
        Args:
            large_clique: Large clique to decompose
            graph: Connectivity graph
            
        Returns:
            List of smaller cliques
        """
        # Simple approach: use graph partitioning
        subgraph = graph.subgraph(large_clique)
        
        # Use greedy approach to create smaller cliques
        remaining_nodes = set(large_clique)
        sub_cliques = []
        
        while remaining_nodes:
            # Start new clique with highest degree node
            degrees = {node: subgraph.degree(node) for node in remaining_nodes}
            start_node = max(degrees, key=degrees.get)
            
            current_clique = {start_node}
            remaining_nodes.remove(start_node)
            
            # Add connected nodes up to maximum size
            for node in list(remaining_nodes):
                if len(current_clique) >= self.config.max_clique_size:
                    break
                if any(subgraph.has_edge(node, clique_node) for clique_node in current_clique):
                    current_clique.add(node)
                    remaining_nodes.remove(node)
            
            sub_cliques.append(current_clique)
        
        return sub_cliques
    
    def _decompose_clique(
        self,
        clique: Set[str],
        pile_geometries: Dict[str, Dict[str, trimesh.Trimesh]]
    ) -> List[GeometricObject]:
        """
        Decompose a single clique into mutually exclusive geometric objects.
        
        Args:
            clique: Set of pile IDs in the clique
            pile_geometries: Pile geometries dictionary
            
        Returns:
            List of geometric objects for this clique
        """
        self.logger.debug(f"Decomposing clique with {len(clique)} piles: {clique}")
        
        geometric_objects = []
        
        for part_type in ['part_1', 'part_2', 'part_3']:
            part_objects = self._decompose_clique_part(clique, part_type, pile_geometries)
            geometric_objects.extend(part_objects)
        
        return geometric_objects
    
    def _decompose_clique_part(
        self,
        clique: Set[str],
        part_type: str,
        pile_geometries: Dict[str, Dict[str, trimesh.Trimesh]]
    ) -> List[GeometricObject]:
        """
        Decompose a specific part type within a clique.
        
        Args:
            clique: Set of pile IDs
            part_type: Part type to decompose
            pile_geometries: Pile geometries dictionary
            
        Returns:
            List of geometric objects for this part type
        """
        # Collect valid geometries for this part type
        valid_piles = []
        valid_geometries = []
        
        for pile_id in clique:
            if (part_type in pile_geometries[pile_id] and 
                pile_geometries[pile_id][part_type] is not None):
                valid_piles.append(pile_id)
                valid_geometries.append(pile_geometries[pile_id][part_type])
        
        if not valid_geometries:
            return []
        
        # If only one pile, create single object
        if len(valid_geometries) == 1:
            return self._create_single_pile_object(
                valid_piles[0], part_type, valid_geometries[0]
            )
        
        # Multi-level exclusion algorithm
        return self._multi_level_exclusion(valid_piles, part_type, valid_geometries)
    
    def _create_single_pile_object(
        self,
        pile_id: str,
        part_type: str,
        geometry: trimesh.Trimesh
    ) -> List[GeometricObject]:
        """
        Create geometric object for a single pile (no overlaps).
        
        Args:
            pile_id: Pile identifier
            part_type: Part type
            geometry: 3D geometry
            
        Returns:
            List with single geometric object
        """
        # Determine material type (simplified - would need pile data)
        material_type = MaterialType.SOIL  # Default, should be determined from pile data
        
        geometric_object = GeometricObject(
            object_id=str(uuid.uuid4()),
            contributing_piles=[pile_id],
            part_type=PartType(part_type.replace('_', ' ').title()),
            material_type=material_type,
            volume=geometry.volume,
            allocation_factor=1.0
        )
        
        return [geometric_object]
    
    def _multi_level_exclusion(
        self,
        pile_ids: List[str],
        part_type: str,
        geometries: List[trimesh.Trimesh]
    ) -> List[GeometricObject]:
        """
        Apply multi-level exclusion algorithm to decompose overlapping geometries.
        
        Args:
            pile_ids: List of pile IDs
            part_type: Part type
            geometries: List of geometries
            
        Returns:
            List of decomposed geometric objects
        """
        geometric_objects = []
        
        try:
            # Level 1: Union of all geometries
            union_geometry = geometries[0]
            for geometry in geometries[1:]:
                union_geometry = union_geometry.union(geometry)
            
            # Level 2: Pairwise intersections
            intersections = {}
            for i in range(len(geometries)):
                for j in range(i + 1, len(geometries)):
                    intersection = geometries[i].intersection(geometries[j])
                    if intersection.volume > self.config.spatial_tolerance:
                        key = tuple(sorted([pile_ids[i], pile_ids[j]]))
                        intersections[key] = intersection
            
            # Level 3: Higher-order intersections (simplified for now)
            # TODO: Implement full multi-level exclusion
            
            # Create objects for non-overlapping regions
            for i, pile_id in enumerate(pile_ids):
                # Start with original geometry
                exclusive_geometry = geometries[i]
                
                # Subtract all intersections involving this pile
                for key, intersection in intersections.items():
                    if pile_id in key:
                        exclusive_geometry = exclusive_geometry.difference(intersection)
                
                # Create object if volume is significant
                if exclusive_geometry.volume > self.config.spatial_tolerance:
                    material_type = MaterialType.SOIL  # Default
                    
                    geometric_object = GeometricObject(
                        object_id=str(uuid.uuid4()),
                        contributing_piles=[pile_id],
                        part_type=PartType(part_type.replace('_', ' ').title()),
                        material_type=material_type,
                        volume=exclusive_geometry.volume,
                        allocation_factor=1.0
                    )
                    geometric_objects.append(geometric_object)
            
            # Create objects for intersections
            for key, intersection in intersections.items():
                contributing_piles = list(key)
                allocation_factor = 1.0 / len(contributing_piles)
                material_type = MaterialType.SOIL  # Default
                
                geometric_object = GeometricObject(
                    object_id=str(uuid.uuid4()),
                    contributing_piles=contributing_piles,
                    part_type=PartType(part_type.replace('_', ' ').title()),
                    material_type=material_type,
                    volume=intersection.volume,
                    allocation_factor=allocation_factor
                )
                geometric_objects.append(geometric_object)
        
        except Exception as e:
            self.logger.error(f"Multi-level exclusion failed: {str(e)}")
            # Fallback: create simple objects
            for i, pile_id in enumerate(pile_ids):
                geometric_objects.extend(
                    self._create_single_pile_object(pile_id, part_type, geometries[i])
                )
        
        return geometric_objects
    
    def _validate_volume_conservation(
        self,
        pile_geometries: Dict[str, Dict[str, trimesh.Trimesh]],
        geometric_objects: List[GeometricObject]
    ) -> bool:
        """
        Validate that volume is conserved in the decomposition.
        
        Args:
            pile_geometries: Original pile geometries
            geometric_objects: Decomposed objects
            
        Returns:
            True if volume conservation is satisfied
        """
        self.logger.debug("Validating volume conservation")
        
        # Calculate original total volume
        original_volume = 0.0
        for pile_id, geometries in pile_geometries.items():
            for part_type, geometry in geometries.items():
                if geometry is not None:
                    original_volume += geometry.volume
        
        # Calculate decomposed total volume
        decomposed_volume = sum(obj.volume for obj in geometric_objects)
        
        # Check conservation within tolerance
        volume_difference = abs(original_volume - decomposed_volume)
        relative_error = volume_difference / original_volume if original_volume > 0 else 0
        
        if relative_error <= self.config.volume_tolerance:
            self.logger.info(f"Volume conservation validated: {relative_error:.4f} relative error")
            return True
        else:
            self.logger.warning(
                f"Volume conservation failed: {relative_error:.4f} relative error "
                f"(tolerance: {self.config.volume_tolerance})"
            )
            return False