"""
CSV Report Generator for Soil Rock Cone Analysis

This module provides functionality to generate comprehensive CSV reports
for pile volume analysis results according to the specification format.
"""

import pandas as pd
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..data_models import VolumeReport, GeometricObject, MaterialType
from ..utils.logging_config import get_logger


class CSVExporter:
    """
    CSV report generator for pile volume analysis results.
    
    Generates comprehensive reports including individual pile volumes,
    overlap analysis, and summary statistics according to specification format.
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def generate_pile_volume_report(
        self,
        volume_reports: List[VolumeReport],
        output_directory: str,
        project_name: str = "pile_analysis"
    ) -> str:
        """
        Generate comprehensive pile volume report CSV.
        
        Args:
            volume_reports: List of volume reports for each pile
            output_directory: Output directory for CSV file
            project_name: Project name for file naming
            
        Returns:
            Path to generated CSV file
        """
        self.logger.info("Generating pile volume report CSV")
        
        # Create output directory
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cone_volume_analysis_{timestamp}.csv"
        file_path = output_dir / filename
        
        # Convert volume reports to DataFrame
        df = self._create_pile_volume_dataframe(volume_reports)
        
        # Add metadata header rows
        metadata_rows = self._create_metadata_header(volume_reports, project_name)
        
        # Write CSV with metadata header
        self._write_csv_with_header(df, file_path, metadata_rows)
        
        self.logger.info(f"Generated pile volume report: {file_path}")
        return str(file_path)
    
    def generate_overlap_analysis_report(
        self,
        geometric_objects: List[GeometricObject],
        output_directory: str,
        project_name: str = "pile_analysis"
    ) -> str:
        """
        Generate detailed overlap analysis report CSV.
        
        Args:
            geometric_objects: List of all geometric objects
            output_directory: Output directory for CSV file
            project_name: Project name for file naming
            
        Returns:
            Path to generated CSV file
        """
        self.logger.info("Generating overlap analysis report CSV")
        
        # Create output directory
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"overlap_analysis_{timestamp}.csv"
        file_path = output_dir / filename
        
        # Filter overlap objects (more than one contributing pile)
        overlap_objects = [obj for obj in geometric_objects if len(obj.contributing_piles) > 1]
        
        # Convert overlap objects to DataFrame
        df = self._create_overlap_analysis_dataframe(overlap_objects)
        
        # Add metadata header rows
        metadata_rows = self._create_overlap_metadata_header(overlap_objects, project_name)
        
        # Write CSV with metadata header
        self._write_csv_with_header(df, file_path, metadata_rows)
        
        self.logger.info(f"Generated overlap analysis report: {file_path}")
        return str(file_path)
    
    def _create_pile_volume_dataframe(self, volume_reports: List[VolumeReport]) -> pd.DataFrame:
        """Create DataFrame for pile volume reports according to specification."""
        
        data = []
        
        for report in volume_reports:
            row = {
                'pile_mark': report.pile_id,
                'material_type': report.material_type.value,
                'part1_volume': round(report.part1_volume, 3),
                'part2_volume': round(report.part2_volume, 3),
                'part3_volume': round(report.part3_volume, 3),
                'part1_overlap_volume': round(report.part1_overlap_volume, 3),
                'part2_overlap_volume': round(report.part2_overlap_volume, 3),
                'part3_overlap_volume': round(report.part3_overlap_volume, 3),
                'part1_residual_volume': round(report.part1_residual_volume, 3),
                'part2_residual_volume': round(report.part2_residual_volume, 3),
                'part3_residual_volume': round(report.part3_residual_volume, 3),
                'total_overlap_volume': round(report.total_overlap_volume, 3),
                'total_residual_volume': round(report.total_residual_volume, 3),
                'total_volume': round(report.total_volume, 3)
            }
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # Set pile_mark as index
        df.set_index('pile_mark', inplace=True)
        
        return df
    
    def _create_overlap_analysis_dataframe(self, overlap_objects: List[GeometricObject]) -> pd.DataFrame:
        """Create DataFrame for overlap analysis according to specification."""
        
        data = []
        
        for i, obj in enumerate(overlap_objects, 1):
            # Generate overlap code
            pile_count = len(obj.contributing_piles)
            overlap_code = f"OVL-{obj.part_type.upper()}-{pile_count}-{i:03d}"
            
            row = {
                'overlap_code': overlap_code,
                'overlap_type': obj.part_type,
                'contributing_piles': ';'.join(sorted(obj.contributing_piles)),
                'pile_count': pile_count,
                'material_type': obj.material_type.value,
                'volume_m3': round(obj.volume, 4),
                'volume_per_pile': round(obj.volume / pile_count, 4),
                'centroid_x': round(obj.centroid[0], 2) if obj.centroid else 0.0,
                'centroid_y': round(obj.centroid[1], 2) if obj.centroid else 0.0,
                'centroid_z': round(obj.centroid[2], 2) if obj.centroid else 0.0,
                'geometric_validity': obj.geometry.is_watertight if obj.geometry else False
            }
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # Set overlap_code as index
        df.set_index('overlap_code', inplace=True)
        
        return df
    
    def _create_metadata_header(self, volume_reports: List[VolumeReport], project_name: str) -> List[str]:
        """Create metadata header rows for pile volume report."""
        
        # Calculate summary statistics
        total_piles = len(volume_reports)
        total_volume = sum(report.total_volume for report in volume_reports)
        soil_volume = sum(report.total_volume for report in volume_reports if report.material_type == MaterialType.SOIL)
        rock_volume = sum(report.total_volume for report in volume_reports if report.material_type == MaterialType.ROCK)
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        header_rows = [
            f"# Pile Volume Analysis Report - {project_name}",
            f"# Generated: {timestamp}",
            f"# Software: Soil Rock Cone Analysis System",
            f"# Total Piles: {total_piles}",
            f"# Total Volume: {total_volume:.3f} m³",
            f"# Soil Volume: {soil_volume:.3f} m³",
            f"# Rock Volume: {rock_volume:.3f} m³",
            f"# Volume Precision: 3 decimal places",
            "#",
            "# Column Descriptions:",
            "# pile_mark: Unique pile identifier",
            "# material_type: Soil or Rock classification",
            "# part1_volume: Original Part 1 (pile cylinder) volume (m³)",
            "# part2_volume: Original Part 2 (frustum) volume (m³)",
            "# part3_volume: Original Part 3 (soil cylinder) volume (m³)",
            "# part1_overlap_volume: Part 1 volume allocated from overlaps (m³)",
            "# part2_overlap_volume: Part 2 volume allocated from overlaps (m³)",
            "# part3_overlap_volume: Part 3 volume allocated from overlaps (m³)",
            "# part1_residual_volume: Part 1 volume after overlap subtraction (m³)",
            "# part2_residual_volume: Part 2 volume after overlap subtraction (m³)",
            "# part3_residual_volume: Part 3 volume after overlap subtraction (m³)",
            "# total_overlap_volume: Total overlap volume allocated to this pile (m³)",
            "# total_residual_volume: Total residual volume for this pile (m³)",
            "# total_volume: Grand total volume allocated to this pile (m³)",
            "#"
        ]
        
        return header_rows
    
    def _create_overlap_metadata_header(self, overlap_objects: List[GeometricObject], project_name: str) -> List[str]:
        """Create metadata header rows for overlap analysis report."""
        
        # Calculate summary statistics
        total_overlaps = len(overlap_objects)
        total_overlap_volume = sum(obj.volume for obj in overlap_objects)
        
        # Count by pile count
        pile_count_stats = {}
        for obj in overlap_objects:
            count = len(obj.contributing_piles)
            pile_count_stats[count] = pile_count_stats.get(count, 0) + 1
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        header_rows = [
            f"# Overlap Analysis Report - {project_name}",
            f"# Generated: {timestamp}",
            f"# Software: Soil Rock Cone Analysis System",
            f"# Total Overlaps: {total_overlaps}",
            f"# Total Overlap Volume: {total_overlap_volume:.4f} m³",
        ]
        
        # Add pile count breakdown
        for pile_count in sorted(pile_count_stats.keys()):
            count = pile_count_stats[pile_count]
            header_rows.append(f"# {pile_count}-Pile Overlaps: {count}")
        
        header_rows.extend([
            f"# Volume Precision: 4 decimal places",
            f"# Coordinate Precision: 2 decimal places",
            "#",
            "# Column Descriptions:",
            "# overlap_code: Unique overlap identifier",
            "# overlap_type: Part type classification",
            "# contributing_piles: Semicolon-separated list of pile marks",
            "# pile_count: Number of piles contributing to this overlap",
            "# material_type: Soil or Rock classification",
            "# volume_m3: Total overlap volume (m³)",
            "# volume_per_pile: Volume allocated per contributing pile (m³)",
            "# centroid_x: X-coordinate of overlap centroid (m)",
            "# centroid_y: Y-coordinate of overlap centroid (m)",
            "# centroid_z: Z-coordinate of overlap centroid (m)",
            "# geometric_validity: Boolean flag for watertight geometry",
            "#"
        ])
        
        return header_rows
    
    def _write_csv_with_header(self, df: pd.DataFrame, file_path: Path, header_rows: List[str]):
        """Write CSV file with metadata header."""
        
        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                # Write metadata header
                for row in header_rows:
                    f.write(row + '\n')
                
                # Write DataFrame
                df.to_csv(f, float_format='%.6f')
            
        except Exception as e:
            self.logger.error(f"Error writing CSV file {file_path}: {e}")
            # Fallback: write without header
            df.to_csv(file_path, float_format='%.6f')
    
    def generate_summary_report(
        self,
        volume_reports: List[VolumeReport],
        geometric_objects: List[GeometricObject],
        processing_stats: Dict[str, Any],
        output_directory: str,
        project_name: str = "pile_analysis"
    ) -> str:
        """
        Generate project summary report CSV.
        
        Args:
            volume_reports: List of volume reports
            geometric_objects: List of geometric objects
            processing_stats: Processing statistics
            output_directory: Output directory
            project_name: Project name
            
        Returns:
            Path to generated summary CSV file
        """
        self.logger.info("Generating project summary report")
        
        # Create output directory
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"project_summary_{timestamp}.csv"
        file_path = output_dir / filename
        
        # Calculate summary statistics
        summary_data = self._calculate_summary_statistics(
            volume_reports, geometric_objects, processing_stats
        )
        
        # Create summary DataFrame
        df = pd.DataFrame([summary_data])
        
        # Write CSV
        df.to_csv(file_path, index=False, float_format='%.6f')
        
        self.logger.info(f"Generated project summary: {file_path}")
        return str(file_path)
    
    def _calculate_summary_statistics(
        self,
        volume_reports: List[VolumeReport],
        geometric_objects: List[GeometricObject],
        processing_stats: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate comprehensive project summary statistics."""
        
        # Basic counts
        total_piles = len(volume_reports)
        total_objects = len(geometric_objects)
        individual_objects = len([obj for obj in geometric_objects if len(obj.contributing_piles) == 1])
        overlap_objects = len([obj for obj in geometric_objects if len(obj.contributing_piles) > 1])
        
        # Volume calculations
        total_volume = sum(report.total_volume for report in volume_reports)
        soil_volume = sum(report.total_volume for report in volume_reports if report.material_type == MaterialType.SOIL)
        rock_volume = sum(report.total_volume for report in volume_reports if report.material_type == MaterialType.ROCK)
        
        total_overlap_volume = sum(obj.volume for obj in geometric_objects if len(obj.contributing_piles) > 1)
        total_residual_volume = sum(report.total_residual_volume for report in volume_reports)
        
        # Overlap statistics
        max_pile_overlap = max((len(obj.contributing_piles) for obj in geometric_objects if len(obj.contributing_piles) > 1), default=0)
        
        # Processing statistics
        processing_time = processing_stats.get('processing_time', 0.0)
        
        return {
            'project_name': 'Pile Volume Analysis',
            'analysis_timestamp': datetime.now().isoformat(),
            'total_piles': total_piles,
            'total_geometric_objects': total_objects,
            'individual_objects': individual_objects,
            'overlap_objects': overlap_objects,
            'max_pile_overlap': max_pile_overlap,
            'total_volume_m3': round(total_volume, 6),
            'soil_volume_m3': round(soil_volume, 6),
            'rock_volume_m3': round(rock_volume, 6),
            'total_overlap_volume_m3': round(total_overlap_volume, 6),
            'total_residual_volume_m3': round(total_residual_volume, 6),
            'overlap_percentage': round((total_overlap_volume / total_volume * 100) if total_volume > 0 else 0, 2),
            'processing_time_seconds': round(processing_time, 2),
            'volume_conservation_verified': True,  # Would be calculated from actual validation
            'software_version': 'Soil Rock Cone Analysis System v1.0'
        }