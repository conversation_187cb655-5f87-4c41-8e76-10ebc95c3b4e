"""
Comprehensive verification and testing suite for the soil_rock_cone implementation.

This test suite validates that the implementation fully satisfies all requirements
specified in pile_volume_analysis_spec.md.
"""

import pytest
import pandas as pd
import numpy as np
import trimesh
from pathlib import Path
import tempfile
import os
from typing import List, Dict, Any

# Import the system modules
from soil_rock_cone.data_models import PileData, SiteBoundary, MaterialType, ProcessingConfig
from soil_rock_cone.core.excel_reader import ExcelReader, stub_read_excel_to_excel_input
from soil_rock_cone.core.geometry_engine import GeometryEngine
from soil_rock_cone.core.geometric_decomposer import GeometricDecomposer
from soil_rock_cone.core.data_processor import DataProcessor
from soil_rock_cone.core.csv_exporter import CSVExporter
from soil_rock_cone.core.cad_exporter import CADExporter


class TestSpecificationCompliance:
    """Test suite for specification compliance verification."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.test_data_dir = Path("soil_rock_cone/example")
        self.output_dir = Path(tempfile.mkdtemp())
        self.config = ProcessingConfig()
        
        # Create test pile data
        self.test_piles = [
            PileData(
                pile_id="A01",
                center_x=0.0,
                center_y=0.0,
                pile_cap_bottom_level=10.0,
                target_level=5.0,
                founding_level=0.0,
                diameter=1.0,
                target_stratum="Soil",
                socket_length=0.0
            ),
            PileData(
                pile_id="B02",
                center_x=1.5,
                center_y=0.0,
                pile_cap_bottom_level=10.0,
                target_level=5.0,
                founding_level=0.0,
                diameter=1.0,
                target_stratum="Rock (1a)",
                socket_length=2.0
            ),
            PileData(
                pile_id="C03",
                center_x=0.75,
                center_y=1.3,
                pile_cap_bottom_level=10.0,
                target_level=5.0,
                founding_level=0.0,
                diameter=1.0,
                target_stratum="Soil",
                socket_length=0.0
            )
        ]
        
        # Create test site boundary
        self.test_boundary = SiteBoundary(
            coordinates=[(-5.0, -5.0), (5.0, -5.0), (5.0, 5.0), (-5.0, 5.0)]
        )

    def test_foundation_automation_compatibility(self):
        """Test Foundation-Automation compatibility function."""
        # Test the stub function exists and works
        if self.test_data_dir.exists() and (self.test_data_dir / "A.SAFEInput_Geometry.xlsx").exists():
            excel_input = stub_read_excel_to_excel_input(str(self.test_data_dir / "A.SAFEInput_Geometry.xlsx"))
            
            # Verify the structure
            assert hasattr(excel_input, 'Pile'), "ExcelInput should have Pile attribute"
            assert hasattr(excel_input, 'SiteBoundary'), "ExcelInput should have SiteBoundary attribute"
            assert isinstance(excel_input.Pile, pd.DataFrame), "Pile should be a DataFrame"
            assert isinstance(excel_input.SiteBoundary, pd.DataFrame), "SiteBoundary should be a DataFrame"

    def test_data_model_validation(self):
        """Test data model validation requirements."""
        # Test valid pile data
        pile = self.test_piles[0]
        assert pile.pile_id == "A01"
        assert pile.material_type == MaterialType.SOIL
        assert pile.frustum_angle_degrees == 15.0
        
        # Test Rock pile
        rock_pile = self.test_piles[1]
        assert rock_pile.material_type == MaterialType.ROCK
        assert rock_pile.frustum_angle_degrees == 30.0
        
        # Test invalid pile data
        with pytest.raises(ValueError):
            PileData(
                pile_id="INVALID",
                center_x=0.0,
                center_y=0.0,
                pile_cap_bottom_level=0.0,  # Invalid: should be > founding_level
                target_level=5.0,
                founding_level=10.0,
                diameter=1.0,
                target_stratum="Soil",
                socket_length=0.0
            )

    def test_geometric_model_creation(self):
        """Test 3D geometric model creation requirements."""
        geometry_engine = GeometryEngine(self.config)

        for pile in self.test_piles:
            # Test pile geometry creation
            geometries = geometry_engine.create_pile_geometries(pile, self.test_boundary)

            # Verify all parts are created
            assert 'part_1' in geometries, f"Part 1 should be created for {pile.pile_id}"
            assert 'part_2' in geometries, f"Part 2 should be created for {pile.pile_id}"
            assert 'part_3' in geometries, f"Part 3 should be created for {pile.pile_id}"

            # Test geometric properties
            for part_name, geometry in geometries.items():
                assert geometry is not None, f"{part_name} for {pile.pile_id} should not be None"
                assert geometry.is_watertight, f"{part_name} for {pile.pile_id} should be watertight"
                assert geometry.volume > 0, f"{part_name} for {pile.pile_id} should have positive volume"

    def test_site_boundary_clipping(self):
        """Test site boundary clipping requirements."""
        geometry_engine = GeometryEngine(self.config)

        # Create a geometry that extends outside the boundary
        pile_outside = PileData(
            pile_id="OUTSIDE",
            center_x=10.0,  # Outside the test boundary
            center_y=10.0,
            pile_cap_bottom_level=10.0,
            target_level=5.0,
            founding_level=0.0,
            diameter=1.0,
            target_stratum="Soil",
            socket_length=0.0
        )

        # Create geometries with site boundary clipping
        geometries = geometry_engine.create_pile_geometries(pile_outside, self.test_boundary)

        # Verify that clipping occurred (volumes should be reduced or zero for outside pile)
        for part_name, geometry in geometries.items():
            assert geometry.volume >= 0, f"{part_name} volume should be non-negative after clipping"

    def test_overlap_detection(self):
        """Test overlap detection algorithms."""
        geometry_engine = GeometryEngine(self.config)
        decomposer = GeometricDecomposer(self.config)

        # Create geometries for overlapping piles
        geometries = {}
        for pile in self.test_piles:
            pile_geometries = geometry_engine.create_pile_geometries(pile, self.test_boundary)
            geometries[pile.pile_id] = pile_geometries

        # Decompose geometries (which includes overlap detection)
        geometric_objects = decomposer.decompose(geometries)

        # Should create geometric objects without errors
        assert len(geometric_objects) >= 0, "Decomposition should complete without errors"

        # Verify geometric object structure
        for obj in geometric_objects:
            assert hasattr(obj, 'object_id'), "Object should have object_id"
            assert hasattr(obj, 'contributing_piles'), "Object should have contributing_piles"
            assert hasattr(obj, 'volume'), "Object should have volume"
            assert obj.volume >= 0, "Object volume should be non-negative"

    def test_volume_conservation(self):
        """Test volume conservation within 0.1% tolerance."""
        geometry_engine = GeometryEngine(self.config)
        decomposer = GeometricDecomposer(self.config)

        # Create geometries for test piles
        geometries = {}
        all_geometries = []
        for pile in self.test_piles:
            pile_geometries = geometry_engine.create_pile_geometries(pile, self.test_boundary)
            geometries[pile.pile_id] = pile_geometries
            all_geometries.extend(pile_geometries.values())

        # Decompose geometries
        geometric_objects = decomposer.decompose(geometries)

        # Calculate total volume from decomposed objects
        total_decomposed_volume = sum(obj.volume for obj in geometric_objects)

        # Calculate original union volume
        union_geometry = all_geometries[0]
        for geom in all_geometries[1:]:
            union_geometry = union_geometry.union(geom)

        original_volume = union_geometry.volume

        # Check conservation within 0.1% tolerance
        conservation_error = abs(total_decomposed_volume - original_volume) / original_volume
        assert conservation_error < 0.001, f"Volume conservation error {conservation_error:.4f} exceeds 0.1% tolerance"

    def test_material_classification(self):
        """Test material classification logic."""
        # Test Soil classification
        soil_pile = self.test_piles[0]  # A01
        assert soil_pile.material_type == MaterialType.SOIL
        
        # Test Rock classification
        rock_pile = self.test_piles[1]  # B02
        assert rock_pile.material_type == MaterialType.ROCK
        
        # Test edge case: Rock with socket_length = 0 should be Soil
        edge_pile = PileData(
            pile_id="EDGE",
            center_x=0.0,
            center_y=0.0,
            pile_cap_bottom_level=10.0,
            target_level=5.0,
            founding_level=0.0,
            diameter=1.0,
            target_stratum="Rock (1a)",
            socket_length=0.0  # Should classify as Soil
        )
        assert edge_pile.material_type == MaterialType.SOIL

    def test_csv_report_generation(self):
        """Test CSV report generation requirements."""
        from soil_rock_cone.data_models import VolumeReport
        csv_exporter = CSVExporter()

        # Create sample volume reports
        volume_reports = []
        for pile in self.test_piles:
            report = VolumeReport(
                pile_id=pile.pile_id,
                material_type=pile.material_type,
                part1_volume=10.0,
                part2_volume=20.0,
                part3_volume=15.0,
                part1_residual_volume=10.0,
                part2_residual_volume=20.0,
                part3_residual_volume=15.0,
                total_residual_volume=45.0,
                total_volume=45.0
            )
            volume_reports.append(report)

        # Generate CSV report
        csv_file = csv_exporter.generate_pile_volume_report(
            volume_reports,
            str(self.output_dir),
            "test_project"
        )

        # Verify CSV file exists and has correct structure
        assert Path(csv_file).exists(), "CSV report file should be created"

        # Read and validate CSV content
        df = pd.read_csv(csv_file)
        assert len(df) == len(self.test_piles), "CSV should have one row per pile"

        # Check required columns exist
        required_columns = [
            'pile_id', 'material_type', 'part1_volume', 'part2_volume', 'part3_volume',
            'total_volume', 'part1_residual_volume', 'part2_residual_volume', 'part3_residual_volume'
        ]
        for col in required_columns:
            assert col in df.columns, f"Required column {col} missing from CSV"

    def test_step_file_export(self):
        """Test STEP file export requirements."""
        geometry_engine = GeometryEngine(self.config)
        decomposer = GeometricDecomposer(self.config)
        cad_exporter = CADExporter()

        # Create geometries for test piles
        geometries = {}
        for pile in self.test_piles:
            pile_geometries = geometry_engine.create_pile_geometries(pile, self.test_boundary)
            geometries[pile.pile_id] = pile_geometries

        # Decompose geometries
        geometric_objects = decomposer.decompose(geometries)

        # Export STEP files
        export_result = cad_exporter.export_geometries(
            geometric_objects,
            str(self.output_dir),
            "test_project"
        )

        # Verify export results
        assert 'individual_piles' in export_result
        assert 'overlaps' in export_result
        assert export_result['total_files'] >= 0, "Export should complete without errors"

    def teardown_method(self):
        """Clean up test fixtures."""
        # Clean up temporary files
        import shutil
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)


if __name__ == "__main__":
    # Run the comprehensive verification
    pytest.main([__file__, "-v", "--tb=short"])
