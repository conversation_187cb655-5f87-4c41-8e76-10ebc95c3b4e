"""
3D Geometry Engine for the Soil Rock Cone system.

This module handles the creation of 3D pile geometries using Trimesh:
- Part 1: Pile cylinder (voiding agent)
- Part 2: Soil/Rock frustum with cylindrical void
- Part 3: Soil cylinder with cylindrical void
- Site boundary clipping operations
"""

import logging
import numpy as np
import trimesh
from typing import Dict, List, Tuple, Any
from shapely.geometry import Polygon
import math

from ..data_models import PileData, SiteBoundary, ProcessingConfig, PartType, MaterialType
from ..utils.logging_config import get_logger


class GeometryEngine:
    """
    3D geometry creation and manipulation engine.
    
    Uses Trimesh for robust 3D operations including mesh creation,
    boolean operations, and volume calculations.
    """
    
    def __init__(self, config: ProcessingConfig):
        """
        Initialize the geometry engine.
        
        Args:
            config: Processing configuration parameters
        """
        self.logger = get_logger(__name__)
        self.config = config
        
    def create_pile_geometries(
        self, 
        pile: PileData, 
        site_boundary: SiteBoundary
    ) -> Dict[str, trimesh.Trimesh]:
        """
        Create all 3D geometries for a single pile.
        
        Args:
            pile: Pile data with geometric parameters
            site_boundary: Site boundary for clipping
            
        Returns:
            Dictionary with Part 1, 2, 3 geometries
        """
        self.logger.debug(f"Creating geometries for pile {pile.pile_id}")
        
        geometries = {}
        
        # Create Part 1: Pile cylinder (voiding agent)
        geometries['part_1'] = self._create_part_1_geometry(pile, site_boundary)
        
        # Create Part 2: Soil/Rock frustum with cylindrical void
        geometries['part_2'] = self._create_part_2_geometry(pile, site_boundary)
        
        # Create Part 3: Soil cylinder with cylindrical void
        geometries['part_3'] = self._create_part_3_geometry(pile, site_boundary)
        
        # Validate all geometries
        for part_name, geometry in geometries.items():
            if geometry is not None:
                self._validate_geometry(geometry, f"{pile.pile_id}_{part_name}")
        
        return geometries
    
    def _create_part_1_geometry(
        self, 
        pile: PileData, 
        site_boundary: SiteBoundary
    ) -> trimesh.Trimesh:
        """
        Create Part 1: Pile cylinder (voiding agent).
        
        This represents the physical pile volume that displaces soil/rock.
        
        Args:
            pile: Pile data
            site_boundary: Site boundary for clipping
            
        Returns:
            Trimesh object for Part 1 geometry
        """
        try:
            # Create cylinder mesh
            cylinder = trimesh.creation.cylinder(
                radius=pile.diameter / 2.0,
                height=pile.part_1_height,
                sections=32  # Number of radial sections for smoothness
            )
            
            # Position cylinder at correct location
            # Bottom of cylinder at founding level, top at pile cap bottom
            translation = np.array([
                pile.center_x,
                pile.center_y,
                pile.founding_level + pile.part_1_height / 2.0
            ])
            cylinder.apply_translation(translation)
            
            # Clip to site boundary
            clipped_cylinder = self._clip_to_site_boundary(cylinder, site_boundary)
            
            self.logger.debug(f"Created Part 1 geometry for {pile.pile_id}: volume = {clipped_cylinder.volume:.3f} m³")
            return clipped_cylinder
            
        except Exception as e:
            self.logger.error(f"Failed to create Part 1 geometry for {pile.pile_id}: {str(e)}")
            return None
    
    def _create_part_2_geometry(
        self, 
        pile: PileData, 
        site_boundary: SiteBoundary
    ) -> trimesh.Trimesh:
        """
        Create Part 2: Soil/Rock frustum with cylindrical void.
        
        This represents the frustum of soil/rock around the pile with
        the pile cylinder subtracted as a void.
        
        Args:
            pile: Pile data
            site_boundary: Site boundary for clipping
            
        Returns:
            Trimesh object for Part 2 geometry
        """
        try:
            # Calculate frustum parameters
            angle_rad = math.radians(pile.frustum_angle_degrees)
            height = pile.part_2_height
            
            if height <= 0:
                self.logger.warning(f"Part 2 height is zero or negative for {pile.pile_id}")
                return None
            
            # Calculate radii
            top_radius = pile.diameter / 2.0
            bottom_radius = top_radius + height * math.tan(angle_rad)
            
            # Create frustum (truncated cone)
            frustum = trimesh.creation.cone(
                radius=bottom_radius,
                height=height,
                sections=32
            )
            
            # Create top cylinder to subtract from cone to make frustum
            if bottom_radius > top_radius:
                # Calculate height of full cone
                full_cone_height = top_radius / math.tan(angle_rad)
                
                # Create full cone and subtract top portion
                full_cone = trimesh.creation.cone(
                    radius=bottom_radius,
                    height=height + full_cone_height,
                    sections=32
                )
                
                # Create cylinder to subtract from top
                top_cylinder = trimesh.creation.cylinder(
                    radius=top_radius + 0.001,  # Slightly larger to ensure clean boolean
                    height=full_cone_height + 0.002,
                    sections=32
                )
                
                # Position top cylinder
                top_cylinder.apply_translation([0, 0, height + full_cone_height/2])
                
                # Create frustum by subtracting top
                frustum = full_cone.difference(top_cylinder)
            
            # Create pile cylinder void
            pile_void = trimesh.creation.cylinder(
                radius=pile.diameter / 2.0,
                height=height + 0.002,  # Slightly taller to ensure clean boolean
                sections=32
            )
            pile_void.apply_translation([0, 0, height/2])
            
            # Subtract pile void from frustum
            frustum_with_void = frustum.difference(pile_void)
            
            # Position at correct location
            translation = np.array([
                pile.center_x,
                pile.center_y,
                pile.target_level + height / 2.0
            ])
            frustum_with_void.apply_translation(translation)
            
            # Clip to site boundary
            clipped_frustum = self._clip_to_site_boundary(frustum_with_void, site_boundary)
            
            self.logger.debug(f"Created Part 2 geometry for {pile.pile_id}: volume = {clipped_frustum.volume:.3f} m³")
            return clipped_frustum
            
        except Exception as e:
            self.logger.error(f"Failed to create Part 2 geometry for {pile.pile_id}: {str(e)}")
            return None
    
    def _create_part_3_geometry(
        self, 
        pile: PileData, 
        site_boundary: SiteBoundary
    ) -> trimesh.Trimesh:
        """
        Create Part 3: Soil cylinder with cylindrical void.
        
        This represents the soil volume around the pile below the target level
        with the pile cylinder subtracted as a void.
        
        Args:
            pile: Pile data
            site_boundary: Site boundary for clipping
            
        Returns:
            Trimesh object for Part 3 geometry
        """
        try:
            height = pile.part_3_height
            
            if height <= 0:
                self.logger.warning(f"Part 3 height is zero or negative for {pile.pile_id}")
                return None
            
            # Calculate outer radius based on frustum projection
            angle_rad = math.radians(pile.frustum_angle_degrees)
            outer_radius = pile.diameter / 2.0 + height * math.tan(angle_rad)
            
            # Create outer cylinder
            outer_cylinder = trimesh.creation.cylinder(
                radius=outer_radius,
                height=height,
                sections=32
            )
            
            # Create pile cylinder void
            pile_void = trimesh.creation.cylinder(
                radius=pile.diameter / 2.0,
                height=height + 0.002,  # Slightly taller to ensure clean boolean
                sections=32
            )
            
            # Subtract pile void from outer cylinder
            cylinder_with_void = outer_cylinder.difference(pile_void)
            
            # Position at correct location
            translation = np.array([
                pile.center_x,
                pile.center_y,
                pile.founding_level + height / 2.0
            ])
            cylinder_with_void.apply_translation(translation)
            
            # Clip to site boundary
            clipped_cylinder = self._clip_to_site_boundary(cylinder_with_void, site_boundary)
            
            self.logger.debug(f"Created Part 3 geometry for {pile.pile_id}: volume = {clipped_cylinder.volume:.3f} m³")
            return clipped_cylinder
            
        except Exception as e:
            self.logger.error(f"Failed to create Part 3 geometry for {pile.pile_id}: {str(e)}")
            return None
    
    def _clip_to_site_boundary(
        self, 
        geometry: trimesh.Trimesh, 
        site_boundary: SiteBoundary
    ) -> trimesh.Trimesh:
        """
        Clip 3D geometry to site boundary polygon.
        
        Args:
            geometry: 3D geometry to clip
            site_boundary: 2D site boundary polygon
            
        Returns:
            Clipped geometry
        """
        try:
            # Create 2D polygon from site boundary
            polygon = Polygon(site_boundary.closed_coordinates)
            
            # Get geometry bounds to determine extrusion height
            bounds = geometry.bounds
            z_min = bounds[0][2]
            z_max = bounds[1][2]
            extrusion_height = z_max - z_min + 10.0  # Add buffer
            
            # Extrude 2D polygon to 3D
            boundary_mesh = trimesh.creation.extrude_polygon(
                polygon,
                height=extrusion_height
            )
            
            # Position boundary mesh to encompass geometry
            boundary_mesh.apply_translation([0, 0, z_min - 5.0])
            
            # Perform boolean intersection
            clipped_geometry = geometry.intersection(boundary_mesh)
            
            return clipped_geometry
            
        except Exception as e:
            self.logger.warning(f"Failed to clip geometry to site boundary: {str(e)}")
            # Return original geometry if clipping fails
            return geometry
    
    def _validate_geometry(self, geometry: trimesh.Trimesh, name: str) -> bool:
        """
        Validate 3D geometry for correctness.
        
        Args:
            geometry: Geometry to validate
            name: Name for logging
            
        Returns:
            True if geometry is valid
        """
        try:
            if geometry is None:
                self.logger.warning(f"Geometry {name} is None")
                return False
            
            # Check if mesh is watertight
            if not geometry.is_watertight:
                self.logger.warning(f"Geometry {name} is not watertight")
            
            # Check volume
            if geometry.volume <= 0:
                self.logger.warning(f"Geometry {name} has zero or negative volume: {geometry.volume}")
                return False
            
            # Check for degenerate faces
            if hasattr(geometry, 'faces') and len(geometry.faces) == 0:
                self.logger.warning(f"Geometry {name} has no faces")
                return False
            
            self.logger.debug(f"Geometry {name} validation passed: volume = {geometry.volume:.3f} m³")
            return True
            
        except Exception as e:
            self.logger.error(f"Geometry validation failed for {name}: {str(e)}")
            return False