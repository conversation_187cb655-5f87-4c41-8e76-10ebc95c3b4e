"""
Main data processor for the Soil Rock Cone system.

This module orchestrates the entire pile volume analysis pipeline:
1. Data loading and validation
2. 3D geometry creation
3. Overlap detection and decomposition
4. Volume calculations
5. Report generation
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, List
import pandas as pd

from ..data_models import PileData, SiteBoundary, ProcessingConfig, VolumeReport
from ..utils.logging_config import get_logger
from .geometry_engine import GeometryEngine
from .geometric_decomposer import GeometricDecomposer


class DataProcessor:
    """
    Main orchestrator for the pile volume analysis pipeline.
    
    Coordinates all processing steps from data input to final reports,
    managing the flow between different system components.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the data processor.
        
        Args:
            config: Configuration dictionary for processing parameters
        """
        self.logger = get_logger(__name__)
        self.config = ProcessingConfig(**config)
        self.geometry_engine = GeometryEngine(self.config)
        self.decomposer = GeometricDecomposer(self.config)
        
        # Processing state
        self.pile_data: List[PileData] = []
        self.site_boundary: SiteBoundary = None
        self.processing_stats = {}
        
    def process(
        self,
        excel_file_path: str,
        site_boundary_coords: List[tuple],
        output_directory: str = "output"
    ) -> Dict[str, Any]:
        """
        Execute the complete pile volume analysis pipeline.
        
        Args:
            excel_file_path: Path to Excel file with pile data
            site_boundary_coords: Site boundary coordinates
            output_directory: Output directory for results
            
        Returns:
            Dictionary with processing results and output file paths
        """
        start_time = time.time()
        
        try:
            self.logger.info("Starting pile volume analysis pipeline")
            
            # Step 1: Load and validate data
            self.logger.info("Step 1: Loading and validating data")
            self._load_data(excel_file_path, site_boundary_coords)
            
            # Step 2: Create 3D geometries
            self.logger.info("Step 2: Creating 3D pile geometries")
            pile_geometries = self._create_geometries()
            
            # Step 3: Detect overlaps and decompose
            self.logger.info("Step 3: Detecting overlaps and decomposing geometries")
            geometric_objects = self._decompose_geometries(pile_geometries)
            
            # Step 4: Calculate volumes
            self.logger.info("Step 4: Calculating volumes and generating reports")
            volume_reports = self._calculate_volumes(geometric_objects)
            
            # Step 5: Generate outputs
            self.logger.info("Step 5: Generating output files")
            output_files = self._generate_outputs(
                volume_reports, geometric_objects, output_directory
            )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Compile results
            result = {
                'success': True,
                'pile_reports': self._create_pile_reports_dataframe(volume_reports),
                'overlap_reports': self._create_overlap_reports_dataframe(geometric_objects),
                'summary': self._create_summary_statistics(volume_reports),
                'output_files': output_files,
                'processing_time': processing_time,
                'processing_stats': self.processing_stats
            }
            
            self.logger.info(f"Pipeline completed successfully in {processing_time:.2f} seconds")
            return result
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def _load_data(self, excel_file_path: str, site_boundary_coords: List[tuple]) -> None:
        """
        Load and validate pile data and site boundary.
        
        Args:
            excel_file_path: Path to Excel file
            site_boundary_coords: Site boundary coordinates
        """
        try:
            # Use real Excel reader if file exists and is valid
            from .excel_reader import read_excel_file

            if Path(excel_file_path).exists() and Path(excel_file_path).suffix.lower() in ['.xlsx', '.xls']:
                self.logger.info(f"Loading pile data from Excel file: {excel_file_path}")

                # Read Excel file
                excel_input = read_excel_file(excel_file_path)

                # Convert Excel data to PileData objects
                self.pile_data = self._convert_excel_to_pile_data(excel_input.Pile)

                # Use site boundary from Excel if available, otherwise use provided coordinates
                if not excel_input.SiteBoundary.empty:
                    site_boundary_coords = self._extract_site_boundary_coords(excel_input.SiteBoundary)
                    self.logger.info("Using site boundary from Excel file")
                else:
                    self.logger.info("Using provided site boundary coordinates")

            else:
                # Fallback to stub data for testing
                self.logger.warning(f"Excel file not found or invalid: {excel_file_path}")
                self.logger.info("Using stub data for testing")
                self.pile_data = self._create_stub_pile_data()

        except Exception as e:
            self.logger.warning(f"Error reading Excel file: {e}")
            self.logger.info("Falling back to stub data for testing")
            self.pile_data = self._create_stub_pile_data()
        
        # Validate site boundary
        self.site_boundary = SiteBoundary(coordinates=site_boundary_coords)
        
        self.logger.info(f"Loaded {len(self.pile_data)} piles")
        self.processing_stats['pile_count'] = len(self.pile_data)
        
    def _create_geometries(self) -> Dict[str, Any]:
        """
        Create 3D geometries for all piles.
        
        Returns:
            Dictionary mapping pile IDs to their geometric objects
        """
        pile_geometries = {}
        
        for pile in self.pile_data:
            self.logger.debug(f"Creating geometry for pile {pile.pile_id}")
            geometries = self.geometry_engine.create_pile_geometries(pile, self.site_boundary)
            pile_geometries[pile.pile_id] = geometries
            
        self.processing_stats['geometries_created'] = len(pile_geometries)
        return pile_geometries
        
    def _decompose_geometries(self, pile_geometries: Dict[str, Any]) -> List[Any]:
        """
        Decompose overlapping geometries into mutually exclusive objects.
        
        Args:
            pile_geometries: Dictionary of pile geometries
            
        Returns:
            List of decomposed geometric objects
        """
        geometric_objects = self.decomposer.decompose(pile_geometries)
        self.processing_stats['geometric_objects'] = len(geometric_objects)
        return geometric_objects
        
    def _calculate_volumes(self, geometric_objects: List[Any]) -> List[VolumeReport]:
        """
        Calculate volume reports for each pile.
        
        Args:
            geometric_objects: List of geometric objects
            
        Returns:
            List of volume reports per pile
        """
        # Group objects by pile and calculate volumes
        pile_volumes = {}
        
        for obj in geometric_objects:
            for pile_id in obj.contributing_piles:
                if pile_id not in pile_volumes:
                    pile_volumes[pile_id] = {
                        'part_1_volume': 0.0,
                        'part_2_volume': 0.0,
                        'part_3_volume': 0.0,
                        'material_type': None
                    }
                
                # Add allocated volume to appropriate part
                allocated_volume = obj.allocated_volume
                if obj.part_type.value == "Part 1":
                    pile_volumes[pile_id]['part_1_volume'] += allocated_volume
                elif obj.part_type.value == "Part 2":
                    pile_volumes[pile_id]['part_2_volume'] += allocated_volume
                elif obj.part_type.value == "Part 3":
                    pile_volumes[pile_id]['part_3_volume'] += allocated_volume
                
                # Set material type
                pile_volumes[pile_id]['material_type'] = obj.material_type
        
        # Create volume reports
        volume_reports = []
        for pile_id, volumes in pile_volumes.items():
            total_volume = (
                volumes['part_1_volume'] + 
                volumes['part_2_volume'] + 
                volumes['part_3_volume']
            )
            
            # For now, assume no overlaps (simplified implementation)
            # In a full implementation, these would be calculated from geometric decomposition
            part1_overlap = 0.0
            part2_overlap = 0.0
            part3_overlap = 0.0

            part1_residual = volumes['part_1_volume'] - part1_overlap
            part2_residual = volumes['part_2_volume'] - part2_overlap
            part3_residual = volumes['part_3_volume'] - part3_overlap

            total_overlap = part1_overlap + part2_overlap + part3_overlap
            total_residual = part1_residual + part2_residual + part3_residual

            report = VolumeReport(
                pile_id=pile_id,
                material_type=volumes['material_type'],
                part1_volume=volumes['part_1_volume'],
                part2_volume=volumes['part_2_volume'],
                part3_volume=volumes['part_3_volume'],
                part1_overlap_volume=part1_overlap,
                part2_overlap_volume=part2_overlap,
                part3_overlap_volume=part3_overlap,
                part1_residual_volume=part1_residual,
                part2_residual_volume=part2_residual,
                part3_residual_volume=part3_residual,
                total_overlap_volume=total_overlap,
                total_residual_volume=total_residual,
                total_volume=total_residual + total_overlap
            )
            volume_reports.append(report)
            
        return volume_reports
        
    def _generate_outputs(
        self,
        volume_reports: List[VolumeReport],
        geometric_objects: List[Any],
        output_directory: str
    ) -> Dict[str, Any]:
        """
        Generate output files (CSV reports, STEP files, etc.).

        Args:
            volume_reports: Volume calculation results
            geometric_objects: Geometric objects for export
            output_directory: Output directory path

        Returns:
            Dictionary with generated file paths and export results
        """
        from .csv_exporter import CSVExporter
        from .cad_exporter import CADExporter

        output_files = {}
        output_path = Path(output_directory)
        output_path.mkdir(parents=True, exist_ok=True)

        try:
            # Generate CSV reports
            self.logger.info("Generating CSV reports")
            csv_exporter = CSVExporter()

            # Pile volume report
            pile_report_file = csv_exporter.generate_pile_volume_report(
                volume_reports, str(output_path), "pile_analysis"
            )
            output_files['pile_volume_report'] = pile_report_file

            # Overlap analysis report
            overlap_report_file = csv_exporter.generate_overlap_analysis_report(
                geometric_objects, str(output_path), "pile_analysis"
            )
            output_files['overlap_analysis_report'] = overlap_report_file

            # Project summary report
            summary_report_file = csv_exporter.generate_summary_report(
                volume_reports, geometric_objects, self.processing_stats,
                str(output_path), "pile_analysis"
            )
            output_files['summary_report'] = summary_report_file

            # Generate STEP files if enabled
            if self.config.output_step_files:
                self.logger.info("Generating STEP files")
                cad_exporter = CADExporter()

                cad_export_result = cad_exporter.export_geometries(
                    geometric_objects, str(output_path), "pile_analysis"
                )
                output_files['cad_export'] = cad_export_result

            self.logger.info(f"Output generation completed. Files saved to: {output_path}")
            return output_files

        except Exception as e:
            self.logger.error(f"Error generating outputs: {e}")
            return output_files
        
    def _create_pile_reports_dataframe(self, volume_reports: List[VolumeReport]) -> pd.DataFrame:
        """Create DataFrame with pile volume reports."""
        data = []
        for report in volume_reports:
            data.append({
                'Pile ID': report.pile_id,
                'Material Type': report.material_type.value,
                'Part 1 Volume (m³)': report.part1_volume,
                'Part 2 Volume (m³)': report.part2_volume,
                'Part 3 Volume (m³)': report.part3_volume,
                'Total Volume (m³)': report.total_volume
            })
        return pd.DataFrame(data)
        
    def _create_overlap_reports_dataframe(self, geometric_objects: List[Any]) -> pd.DataFrame:
        """Create DataFrame with overlap analysis."""
        data = []
        for obj in geometric_objects:
            if len(obj.contributing_piles) > 1:  # Only overlapping objects
                data.append({
                    'Object ID': obj.object_id,
                    'Contributing Piles': ', '.join(obj.contributing_piles),
                    'Part Type': obj.part_type.value,
                    'Material Type': obj.material_type.value,
                    'Volume (m³)': obj.volume,
                    'Allocation Factor': obj.allocation_factor
                })
        return pd.DataFrame(data)
        
    def _create_summary_statistics(self, volume_reports: List[VolumeReport]) -> Dict[str, Any]:
        """Create summary statistics."""
        total_volume = sum(report.total_volume for report in volume_reports)
        soil_volume = sum(
            report.total_volume for report in volume_reports 
            if report.material_type.value == "Soil"
        )
        rock_volume = sum(
            report.total_volume for report in volume_reports 
            if report.material_type.value == "Rock"
        )
        
        return {
            'total_piles': len(volume_reports),
            'total_volume_m3': total_volume,
            'soil_volume_m3': soil_volume,
            'rock_volume_m3': rock_volume,
            'processing_stats': self.processing_stats
        }

    def _convert_excel_to_pile_data(self, pile_df: pd.DataFrame) -> List[PileData]:
        """
        Convert Excel DataFrame to list of PileData objects.

        Args:
            pile_df: DataFrame with pile data from Excel

        Returns:
            List of validated PileData objects
        """
        pile_data_list = []

        for _, row in pile_df.iterrows():
            try:
                # Determine target level based on target stratum
                target_level = self._determine_target_level(row)

                pile_data = PileData(
                    pile_id=str(row['Pile Mark']),
                    center_x=float(row['X (m)']),
                    center_y=float(row['Y (m)']),
                    pile_cap_bottom_level=float(row['Pile Cap Bottom Level (mPD)']),
                    target_level=target_level,
                    founding_level=float(row['Founding Level (mPD)']),
                    diameter=float(row['Pile Shaft Diameter (m)']),
                    target_stratum=str(row['Target Stratum']),
                    socket_length=float(row['Socket Length (m)'])
                )

                pile_data_list.append(pile_data)

            except Exception as e:
                self.logger.error(f"Error processing pile {row.get('Pile Mark', 'Unknown')}: {e}")
                continue

        self.logger.info(f"Successfully converted {len(pile_data_list)} piles from Excel data")
        return pile_data_list

    def _determine_target_level(self, row: pd.Series) -> float:
        """
        Determine target level based on target stratum according to specification.

        Args:
            row: DataFrame row with pile data

        Returns:
            Target level (mPD)
        """
        target_stratum = str(row['Target Stratum']).lower()
        socket_length = float(row['Socket Length (m)'])

        # Logic from specification:
        # - "SPTN30 Level (mPD)" for Target Stratum contains "Soil", or Target Stratum contains "Rock" with Socket Length = 0
        # - "Rock1a Level (mPD)" for Target Stratum == "Rock (1a)"
        # - etc.

        if 'soil' in target_stratum or ('rock' in target_stratum and socket_length == 0):
            return float(row.get('SPTN30 Level (mPD)', row.get('Pile Cap Bottom Level (mPD)', 0) - 5))
        elif target_stratum == 'rock (1a)':
            return float(row.get('Rock1a Level (mPD)', row.get('Pile Cap Bottom Level (mPD)', 0) - 10))
        elif target_stratum == 'rock (1b)':
            return float(row.get('Rock1b Level (mPD)', row.get('Pile Cap Bottom Level (mPD)', 0) - 10))
        elif target_stratum == 'rock (1c)':
            return float(row.get('Rock1c Level (mPD)', row.get('Pile Cap Bottom Level (mPD)', 0) - 10))
        elif target_stratum == 'rock (1d)':
            return float(row.get('Rock1d Level (mPD)', row.get('Pile Cap Bottom Level (mPD)', 0) - 10))
        elif target_stratum == 'rock (2)':
            return float(row.get('Rock2 Level (mPD)', row.get('Pile Cap Bottom Level (mPD)', 0) - 15))
        else:
            # Default fallback
            return float(row.get('Pile Cap Bottom Level (mPD)', 0) - 10)

    def _extract_site_boundary_coords(self, boundary_df: pd.DataFrame) -> List[tuple]:
        """
        Extract site boundary coordinates from DataFrame.

        Args:
            boundary_df: DataFrame with site boundary data

        Returns:
            List of (x, y) coordinate tuples
        """
        coords = []

        # Try different possible column names
        x_col = None
        y_col = None

        for col in boundary_df.columns:
            col_lower = col.lower()
            if 'x' in col_lower and x_col is None:
                x_col = col
            elif 'y' in col_lower and y_col is None:
                y_col = col

        if x_col is None or y_col is None:
            # Fallback: assume first two columns are X, Y
            if len(boundary_df.columns) >= 2:
                x_col = boundary_df.columns[0]
                y_col = boundary_df.columns[1]
            else:
                raise ValueError("Cannot determine X, Y columns in site boundary data")

        for _, row in boundary_df.iterrows():
            try:
                x = float(row[x_col])
                y = float(row[y_col])
                coords.append((x, y))
            except (ValueError, TypeError):
                continue

        return coords

    def _create_stub_pile_data(self) -> List[PileData]:
        """Create stub pile data for testing when Excel file is not available."""
        return [
            PileData(
                pile_id="P001",
                center_x=10.0,
                center_y=10.0,
                pile_cap_bottom_level=100.0,
                target_level=95.0,
                founding_level=85.0,
                diameter=1.2,
                target_stratum="Dense Sand",
                socket_length=0.0
            ),
            PileData(
                pile_id="P002",
                center_x=15.0,
                center_y=10.0,
                pile_cap_bottom_level=100.0,
                target_level=94.0,
                founding_level=80.0,
                diameter=1.5,
                target_stratum="Rock",
                socket_length=5.0
            )
        ]